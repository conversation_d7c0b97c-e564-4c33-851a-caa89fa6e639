<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber Reach - Cyber Calendar</title>
    <link rel="stylesheet" href="../css/styles1.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <img src="../images/Cyber Reach Logo Vector Black.png" alt="Cyber Reach Logo">
        <!-- <h1>Cybe<span>R</span>each</h1> -->
        <p>Your Guide to Cybersecurity</p>
    </header>

    <nav>
        <a href="../index.html">Home</a>
    </nav>

    <main>
        <h1>Cyber Calendar</h1>

        <p>
            Cyber Calendar, an initiative by cybeReach, aims to provide cyber enthusiasts with valuable insights into the rich history, current developments, and future events within the captivating domain of cybersecurity. This platform offers a comprehensive view of the evolution of cyber-related events, spanning from historical milestones to present-day advancements and future projections.
        </p>

        <a href="#">Add Event</a>

        <div class="calendar-container">
            <div class="calendar">
                <!-- Days of the week -->
                <span class="dayname">Monday</span>
                <span class="dayname">Tuesday</span>
                <span class="dayname">Wednesday</span>
                <span class="dayname">Thursday</span>
                <span class="dayname">Friday</span>
                <span class="dayname">Saturday</span>
                <span class="dayname">Sunday</span>
    
                <!-- Calendar days with day numbers -->
                <div class="day">
                    <div class="day-number">1</div>
                </div>
                <div class="day">
                    <div class="day-number">2</div>
                </div>
                <div class="day">
                    <div class="day-number">3</div>
                  </div>
                  <div class="day">
                    <div class="day-number">4</div>
                  </div>
                  <div class="day">
                    <div class="day-number">5</div>
                  </div>
                  <div class="day">
                    <div class="day-number weekend">6</div>
                  </div>
                  <div class="day">
                    <div class="day-number weekend">7</div>
                  </div>
                  <div class="day">
                    <div class="day-number">8</div>
                  </div>
                  <div class="day">
                    <div class="day-number">9</div>
                  </div>
                  <div class="day">
                    <div class="day-number">10</div>
                  </div>
                  <div class="day">
                    <div class="day-number">11</div>
                  </div>
                  <div class="day">
                    <div class="day-number">12</div>
                  </div>
                  <div class="day">
                    <div class="day-number">13</div>
                  </div>
                  <div class="day">
                    <div class="day-number">14</div>
                  </div>
                  <div class="day">
                    <div class="day-number">15</div>
                  </div>
                  <div class="day">
                    <div class="day-number">16</div>
                  </div>
                  <div class="day">
                    <div class="day-number">17</div>
                  </div>
                  <div class="day">
                    <div class="day-number">18</div>
                  </div>
                  <div class="day">
                    <div class="day-number">19</div>
                  </div>
                  <div class="day">
                    <div class="day-number">20</div>
                  </div>
                  <div class="day">
                    <div class="day-number">21</div>
                  </div>
                  <div class="day">
                    <div class="day-number">22</div>
                  </div>
                  <div class="day">
                    <div class="day-number">23</div>
                  </div>
                  <div class="day">
                    <div class="day-number">24</div>
                  </div>
                  <div class="day">
                    <div class="day-number">25</div>
                  </div>
                  <div class="day">
                    <div class="day-number">26</div>
                  </div>
                  <div class="day">
                    <div class="day-number">27</div>
                  </div>
                  <div class="day">
                    <div class="day-number">28</div>
                  </div>
                  <div class="day">
                    <div class="day-number">29</div>
                  </div>
                  <div class="day">
                    <div class="day-number">30</div>
                  </div>
                  <div class="day">
                    <div class="day-number">31</div>
                  </div>
                <!-- Continue for the rest of the month -->
    
                <!-- Events -->
                <div class="event event-weekend-1">Cyber Event 1</div>
                <div class="event" style="
                    border-left-color:#a4123f;
                    grid-column: 4 / span 1;
                    grid-row: 4;
                    background-color: darkblue;
                    text-align:end;
                    align-self:auto;
                    color: white;
                    margin-top: -5px;">Cyber Event 2</div>
            </div>
        </div>

        <div id="october23-container">
            <div id="leftcal">
              <h1 style="color: #a4123f;">OCTOBER</h1>
              <h1>2023</h1>

              <h3><span style="color: #a4123f;">01</span> &nbsp; Release of the "Creeper" Virus</h3>
              <div class="underline"></div>

              <h3><span style="color: #a4123f;">21</span> &nbsp; Dyn Cyberattack</h3>
              <div class="underline"></div>

              <h3><span style="color: #a4123f;">28</span> &nbsp; Bill Gates Birthday</h3>
              <div class="underline"></div>
            </div>
            <div id="rightcal">
              <table>
                <tr>
                  <th>Sun</th>
                  <th id="borCol">Mon</th>
                  <th>Tue</th>
                  <th id="borCol">Wed</th>
                  <th>Thu</th>
                  <th id="borCol">Fri</th>
                  <th>Sat</th>
                </tr>
                <tr>
                  <td id="eve">1</td>
                  <td>2</td>
                  <td>3</td>
                  <td>4</td>
                  <td>5</td>
                  <td>6</td>
                  <td>7</td>
                </tr>
                <tr>
                  <td>8</td>
                  <td>9</td>
                  <td>10</td>
                  <td>11</td>
                  <td>12</td>
                  <td>13</td>
                  <td>14</td>
                </tr>
                <tr>
                  <td>15</td>
                  <td>16</td>
                  <td>17</td>
                  <td>18</td>
                  <td>19</td>
                  <td>20</td>
                  <td id="eve">21</td>
                </tr>
                <tr>
                  <td>22</td>
                  <td>23</td>
                  <td>24</td>
                  <td>25</td>
                  <td>26</td>
                  <td>27</td>
                  <td id="eve">28</td>
                </tr>
                <tr>
                  <td>29</td>
                  <td>30</td>
                  <td>31</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
              </table>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2023 Cyber Reach. All rights reserved.</p>
    </footer>
</body>
</html>
