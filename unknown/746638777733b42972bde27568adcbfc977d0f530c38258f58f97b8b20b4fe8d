body {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  background-color: black;
}


.navbar {
  position: fixed; /* Keep the position fixed */
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000; /* Ensure navbar is above other content */
  background-color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-list.active {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 75px;
  left: -5px;
  width: 100%;
  background-color: #1D3670;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-top: 20px;
  z-index: 1;
  padding-left: 2px;
}



.logo-container img {
  height: 60px; /* Increased logo size to 60px */
  margin-right: 10px;
}


.menu-icon {
  font-size: 24px;
  color: black;
  cursor: pointer;
  display: none;
}


.nav-list {
  list-style-type: none;
  display: flex;
  align-items: center;
}

.nav-list li {
  margin-right: 20px;
}

.nav-list a {
  text-decoration: none;
  color: black;
  transition: color 0.3s ease-in-out;
}

.nav-list a:hover {
  color: #FFD700; /* Gold/Yellow */
}



@media only screen and (max-width: 768px) {
  .nav-list {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 60px;
    left: 0;
    width: 100%;
    background-color: #1D3670;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    margin: 3px; /* Adjust the margin as needed */
    z-index: 1;
  }



  .menu-icon {
    display: block;
  }

}

.main-heading-slide {
  position: relative;
  height: 100vh;
  overflow: hidden;
  color: #FFFFFF; /* Text color */
}

.background-image {
  background-image: url('../images/White\ Aesthetic\ Inspirational\ Desktop\ Wallpaper\ .png');
  opacity: 1; /* Replace with your image URL */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.main-heading-slide {
  position: relative;
  height: 100vh;
  overflow: hidden;
  color:  #3355A2; /* Text color */
}

.main-heading-slide h1.glowing-text {
  font-size: 7rem; /* Adjust the font size as needed */
  font-weight: bold;
  margin-bottom: 10px;
  animation: glowAnimation 5s infinite;
}

.main-heading-slide h1.glowing-text span {
  font-size: 9rem;
}


@media only screen and (max-width: 768px) {
  .glowing-text {
    font-size: 2rem;
  }
}

  @media only screen and (max-width: 1200px) {
    .main-heading-slide h1.glowing-text {
      font-size: 4rem; /* Adjust the font size for medium-sized screens */
    }
    .main-heading-slide h1.glowing-text span {
      font-size: 6rem; /* Adjust the span font size for medium-sized screens */
    }
  }

  @media only screen and (max-width: 768px) {
    .main-heading-slide h1.glowing-text {
      font-size: 3rem; /* Adjust the font size for smaller screens */
    }
    .main-heading-slide h1.glowing-text span {
      font-size: 5rem; /* Adjust the span font size for smaller screens */
    }
  }

  @keyframes glowAnimation {
    0% {
      text-shadow: 0 0 2px #3355A2, 0 0 5px #3355A2, 0 0 8px #3355A2;
    }
    50% {
      text-shadow: 0 0 2px #1D3670, 0 0 5px #1D3670, 0 0 8px #1D3670;
    }
    100% {
      text-shadow: 0 0 2px #3355A2, 0 0 5px #3355A2, 0 0 8px #3355A2;
    }
  }

  .cyber-calendar-slide {
    position: relative;
    height: 50vh; /* Adjust the height as needed */
    overflow: hidden;
    color: white;
  }

  .background-video {
    object-fit: cover;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .cyber-calendar-slide .content {
    text-align: center;
    z-index: 2;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%; /* Adjust the width as needed */
    padding: 20px;
    box-sizing: border-box;
  }

  .cyber-calendar-slide h2 {
    font-size: 4rem;
    margin-bottom: 10px;
    padding-top: 20px;
  }

  .cyber-calendar-slide p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .cyber-calendar-slide .btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 1.2rem;
    text-decoration: none;
    color: black;
    background-color: #FF6680;
    border-radius: 5px;
    transition: background-color 0.3s ease-in-out;
  }

  .cyber-calendar-slide .btn:hover {
    background-color: #FF3355;
  }

  @media only screen and (max-width: 768px) {
    .cyber-calendar-slide .content {
      width: 50%; /* Adjust the width for smaller screens */
    }
  }










  .animated h2,
.animated p,
.animated .btn {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 1s ease, transform 1s ease;
}

/* Apply styles when the section is in view */
.animated.in-view h2,
.animated.in-view p,
.animated.in-view .btn {
  opacity: 1;
  transform: translateY(0);
}
.animated h2,
.animated p,
.animated .btn {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 1s ease, transform 1s ease;
}

/* Apply styles when the section is in view */
.animated.in-view h2,
.animated.in-view p,
.animated.in-view .btn {
  opacity: 1;
  transform: translateY(0);
}

@media only screen and (max-width: 320px) {
  .main-heading-slide h1.glowing-text {
    font-size: 2rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 3rem;
  }


  .cyber-calendar-slide h2 {
    font-size: 1.5rem;
  }

  .cyber-calendar-slide p {
    font-size: 0.9rem;
  }

  .cyber-calendar-slide .content {
    width: 100%; /* Adjust width for 320px screens */
    padding: 10px; /* Adjust padding for 320px screens */
  }


}

@media only screen and (max-width: 375px) {
  .main-heading-slide h1.glowing-text {
    font-size: 3rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 4rem;
  }


  .cyber-calendar-slide h2 {
    font-size: 1.8rem;
  }

  .cyber-calendar-slide p {
    font-size: 1rem;
  }


}

@media only screen and (max-width: 480px) {
  .main-heading-slide h1.glowing-text {
    font-size: 2rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 5rem;
  }



  .cyber-calendar-slide h2 {
    font-size: 2.2rem;
  }

  .cyber-calendar-slide p {
    font-size: 1.2rem;
  }

}

.scam-diaries {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.scam-diaries {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.scam-diaries .background-image {
  background-image: url('../images/Untitled\ design\ \(3\).jpg'); /* Add your background image path */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.scam-diaries .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white; /* Adjust the text color */
  z-index: 1;
  text-align: center;
}

.scam-diaries h2 {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: white; /* Change the text color to a contrasting one */
}

.scam-diaries p {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: white /* Change the text color to a contrasting one */
}

.scam-diaries .btn {
  display: inline-block;
  padding: 15px 30px;
  font-size: 1.2rem;
  text-decoration: none;
  color: #FFF;
  background-color: #39408C;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
  margin-top: 20px; /* Adjust spacing if needed */
}

.scam-diaries .btn:hover {
  background-color: #140F47;
}

@media only screen and (max-width: 768px) {
  .scam-diaries .content {
    width: 50%; /* Adjust the width for smaller screens */
  }

  .scam-diaries h2 {
    font-size: 1.5rem;
  }

  .scam-diaries p {
    font-size: 1rem;
  }

  .scam-diaries .btn {
    font-size: 0.8rem;
  }
}

@media only screen and (max-width: 768px) {
  .cyber-calendar-slide .btn {
    padding: 10px 20px;
    font-size: 1rem;
  }
}

@media only screen and (max-width: 480px) {
  .cyber-calendar-slide .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media only screen and (max-width: 320px) {
  .cyber-calendar-slide .btn {
    font-size: 0.9rem; /* Adjust font size for 320px screens */
    padding: 10px 20px; /* Adjust padding for 320px screens */
  }

}

.blogs {
  position: relative;
  height: 50vh;
  overflow: hidden;
  color: white;
}

.background-video {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.blogs .content {
  text-align: center;
  z-index: 2;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  padding: 20px;
  box-sizing: border-box;
}

.blogs h2 {
  font-size: 4rem;
  margin-bottom: 10px;
}

.blogs p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.blogs .btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1.2rem;
  text-decoration: none;
  color: white;
  background-color: #452180;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
}

.blogs .btn:hover {
  background-color: #100432;
}

@media only screen and (max-width: 768px) {
  .blogs .content {
    width: 80%; /* Adjust the width for smaller screens */
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .blogs h2 {
    font-size: 3rem;
  }

  .blogs p {
    font-size: 1rem;
  }

  .blogs .btn {
    font-size: 1rem; /* Adjust font size for smaller screens */
  }
}

@media only screen and (max-width: 480px) {
  .blogs .content {
    width: 90%; /* Further adjust the width for even smaller screens */
  }

  .blogs h2 {
    font-size: 2rem;
  }

  .blogs p {
    font-size: 0.8rem;
  }

  .blogs .btn {
    font-size: 0.8rem;
  }
}

/* Footer Styles */

footer {
  background-color: #1E90FF;
  color: white;
  padding: 30px 0;
  transition: opacity 1s ease, transform 1s ease;
}

.footer-content {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

/* Follow Us Section Styles */
.follow-us,
.contact-us {
  flex: 1;
  margin: 0 20px;
}

.follow-us h3,
.contact-us h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.social-icons {
  display: flex;
  gap: 10px;
}

.social-icons img {
  width: 30px; /* Adjust the size as needed */
  height: 30px;
}

/* Contact Us Section Styles */
.contact-us .email-icon {
  display: flex;
  align-items: center;
}

.email-icon img {
  width: 25px; /* Adjust the size as needed */
  height: 25px;
  margin-right: 10px;
}


.email-icon a {
  color: white;
  text-decoration: none;
}

/* Copyright Section Styles */
.copyright {
  text-align: center;
  margin-top: 20px;
}

/* Responsive Styles */
@media only screen and (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
  }

  .follow-us,
  .contact-us {
    margin: 10px 0; /* Adjust margin for responsiveness */
    text-align: center;
  }
}

.animated.in-view {
  opacity: 1;
  transform: translateY(0);
}

.about-cybereach {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.about-cybereach .background-image {
  background-image: url('../images/0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ \(1\).png');
  opacity: 0.7;/* Add the path to your background image */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.about-cybereach .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white; /* Adjust the text color */
  z-index: 1;
  text-align: center;
}

.about-cybereach h2 {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: white; /* Change the text color to a contrasting one */
}

.about-cybereach p {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: white; /* Change the text color to a contrasting one */
}

.about-cybereach .btn {
  display: inline-block;
  padding: 15px 30px;
  font-size: 1.2rem;
  text-decoration: none;
  color: black;
  background-color: #66ff66;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
  margin-top: 20px; /* Adjust spacing if needed */
}

.about-cybereach .btn:hover {
  background-color: #009c1a;
}

@media only screen and (max-width: 768px) {
  .about-cybereach .content {
    width: 50%; /* Adjust the width for smaller screens */
  }

  .about-cybereach h2 {
    font-size: 3rem;
  }

  .about-cybereach p {
    font-size: 1rem;
  }

  .about-cybereach .btn {
    font-size: 0.8rem;
    margin-bottom: 40px
  }
}

@media only screen and (max-width: 425px) {
  .about-cybereach .btn {
    padding: 12px 24px; /* Adjust padding for smaller screens */
    font-size: 0.9rem; /* Adjust font size for smaller screens */
    display: block;
    margin-bottom: 85px; /* Center the button */
  }
  }

@media only screen and (max-width: 768px){
.cyber-calendar-slide .btn {
  margin-bottom: 80px;
}
}

/* Enhanced UI Styles and Newspaper Section */

/* Newspaper Cutout Section */
.cyber-news-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 80px 20px;
  position: relative;
  overflow: hidden;
}

.cyber-news-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 118, 117, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255, 177, 153, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.newspaper-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.newspaper-header {
  text-align: center;
  margin-bottom: 50px;
}

.newspaper-title {
  font-family: 'Times New Roman', serif;
  font-size: 3.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 10px;
  letter-spacing: 2px;
}

.newspaper-subtitle {
  font-family: 'Georgia', serif;
  font-size: 1.2rem;
  color: #7f8c8d;
  font-style: italic;
  border-bottom: 3px double #34495e;
  display: inline-block;
  padding-bottom: 10px;
}

.newspaper-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-top: 40px;
}

.main-story {
  background: #fff;
  border: 2px solid #2c3e50;
  border-radius: 0;
  box-shadow:
    0 0 0 1px #2c3e50,
    5px 5px 0 0 rgba(44, 62, 80, 0.2);
  padding: 25px;
  position: relative;
  transform: rotate(-0.5deg);
  transition: all 0.3s ease;
}

.main-story:hover {
  transform: rotate(0deg) scale(1.02);
  box-shadow:
    0 0 0 1px #2c3e50,
    8px 8px 0 0 rgba(44, 62, 80, 0.3);
}

.main-story::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(44, 62, 80, 0.1) 2px,
    rgba(44, 62, 80, 0.1) 4px
  );
  z-index: -1;
  border-radius: 0;
}

.story-headline {
  font-family: 'Times New Roman', serif;
  font-size: 2.2rem;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1.2;
  margin-bottom: 15px;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 10px;
}

.story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  border-top: 1px solid #bdc3c7;
  border-bottom: 1px solid #bdc3c7;
}

.story-date {
  font-family: 'Georgia', serif;
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: bold;
}

.story-category {
  background: #e74c3c;
  color: white;
  padding: 5px 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.story-summary {
  font-family: 'Georgia', serif;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: justify;
}

.story-bullets {
  background: #ecf0f1;
  border-left: 4px solid #3498db;
  padding: 15px 20px;
  margin: 20px 0;
}

.story-bullets h4 {
  font-family: 'Times New Roman', serif;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.story-bullets ul {
  list-style: none;
  padding: 0;
}

.story-bullets li {
  font-family: 'Georgia', serif;
  color: #34495e;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.story-bullets li::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: #3498db;
  font-size: 0.8rem;
}

.sidebar-stories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar-story {
  background: #fff;
  border: 1px solid #bdc3c7;
  padding: 20px;
  position: relative;
  transform: rotate(0.3deg);
  transition: all 0.3s ease;
  box-shadow: 3px 3px 0 0 rgba(189, 195, 199, 0.3);
}

.sidebar-story:nth-child(even) {
  transform: rotate(-0.3deg);
}

.sidebar-story:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: 5px 5px 0 0 rgba(189, 195, 199, 0.5);
  z-index: 10;
}

.sidebar-headline {
  font-family: 'Times New Roman', serif;
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1.3;
  margin-bottom: 10px;
}

.sidebar-summary {
  font-family: 'Georgia', serif;
  font-size: 0.95rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 10px;
}

.sidebar-date {
  font-family: 'Georgia', serif;
  font-size: 0.8rem;
  color: #95a5a6;
  font-style: italic;
}

.read-more-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  font-family: 'Georgia', serif;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.read-more-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #e74c3c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.news-refresh-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 25px;
  font-family: 'Georgia', serif;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 20px auto;
  display: block;
}

.news-refresh-btn:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Responsive Design for Newspaper */
@media (max-width: 768px) {
  .newspaper-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .newspaper-title {
    font-size: 2.5rem;
  }

  .main-story {
    transform: rotate(0deg);
    padding: 20px;
  }

  .story-headline {
    font-size: 1.8rem;
  }

  .sidebar-story {
    transform: rotate(0deg);
  }
}